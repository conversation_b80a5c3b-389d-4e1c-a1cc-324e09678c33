import { PrismaService } from '@/lib/orm/orm.service';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { User } from 'generated/prisma/client';
import * as bcrypt from 'bcrypt';
import { jwtTokens, jwtTokensPayload } from './dto/jwtTokents.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
  ) {}

  async login(email: string, password: string): Promise<jwtTokens> {
    const user: User | null = (await this.prisma.user.findUnique({
      where: { email },
      select: { id: true, password: true },
    })) as User;
    if (!user) {
      throw new UnauthorizedException('Wrong Email');
    }
    const rightPassword = await bcrypt.compare(password, user.password);
    if (!rightPassword) {
      throw new UnauthorizedException('Wrong Password');
    }

    return this.jwtTokens({ email: user.email, sub: user.id });
  }

  async register(
    email: string,
    password: string,
    name: string,
  ): Promise<jwtTokens> {
    const user: User | null = (await this.prisma.user.findUnique({
      where: { email },
    })) as User;
    if (user) {
      throw new UnauthorizedException('User already exists');
    }
    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = await this.prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
      },
    });
    return this.jwtTokens({ email: newUser.email, sub: newUser.id });
  }

  verify(token: jwtTokens) {
    const { access_token } = token;
    const payload = this.jwtService.verify(access_token);
    console.log(payload);

    return true;
  }

  private jwtTokens(payload: jwtTokensPayload): jwtTokens {
    return {
      access_token: this.jwtService.sign(payload, {
        expiresIn: '30m',
      }),
      refresh_token: this.jwtService.sign(payload, {
        expiresIn: '7d',
      }),
    };
  }
}
