import { Controller, Body, HttpCode, HttpStatus, Get } from '@nestjs/common';
import { AuthService } from './auth.service';
import {
  LoginDto,
  RegisterDto,
  VerifyTokenDto,
  jwtTokens,
} from './dto/jwtTokents.dto';

@Controller()
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Get('/ping')
  ping(): string {
    return 'pong';
  }

  @Get('login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: LoginDto): Promise<jwtTokens> {
    const { email, password } = loginDto;
    return this.authService.login(email, password);
  }

  @Get('register')
  @HttpCode(HttpStatus.CREATED)
  async register(@Body() registerDto: RegisterDto): Promise<jwtTokens> {
    const { email, password, name } = registerDto;
    return this.authService.register(email, password, name);
  }

  @Get('verify')
  @HttpCode(HttpStatus.OK)
  verify(@Body() verifyTokenDto: VerifyTokenDto): { valid: boolean } {
    const isValid = this.authService.verify(verifyTokenDto);
    return { valid: isValid };
  }
}
